import {
    checkSocialNetworkUrls,
    cleanUrl,
    Day,
    formatSocialNetworkUrls,
    InformationUpdateAttributeValue,
    InformationUpdateErrorReason,
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    informationUpdateSupportedKey,
    isValidUrl,
    PlatformKey,
    SocialNetworkKey,
    timeRegex,
    urlRegex,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

// import { MergedInformationUpdateByRestaurantId } from ':modules/information-updates/information-update-model';

// type FinalType = MergedInformationUpdateByRestaurantId;

export const mergedInformationUpdateJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'MergedInformationUpdate',
    type: 'object',
    additionalProperties: false,
    properties: {
        restaurantId: {
            type: 'string',
            format: 'objectId',
        },
        mergedInformationUpdateByPlatform: {
            type: 'array',
            items: {
                $ref: '#/definitions/MergedInformationUpdate',
            },
            default: [],
        },
        platformsWithPendingOperations: {
            type: 'array',
            items: {
                enum: Object.values(PlatformKey),
            },
            default: [],
        },
        hasOngoingOperation: {
            type: 'boolean',
            default: false,
        },
    },
    required: ['restaurantId', 'mergedInformationUpdateByPlatform', 'platformsWithPendingOperations'],
    definitions: {
        MergedInformationUpdate: {
            type: 'object',
            additionalProperties: false,
            properties: {
                platformState: {
                    $ref: '#/definitions/PlatformState',
                },
                previousData: {
                    $ref: '#/definitions/Data',
                },
                mergedData: {
                    $ref: '#/definitions/Data',
                },
                lastValidatedAt: {
                    type: 'string',
                    format: 'date-time',
                    nullable: true,
                },
            },
            required: ['platformState', 'previousData', 'mergedData'],
            title: 'MergedInformationUpdate',
        },
        PlatformState: {
            type: 'object',
            additionalProperties: false,
            properties: {
                key: {
                    enum: informationUpdateSupportedKey,
                },
                status: {
                    enum: Object.values(InformationUpdatePlatformStateStatus),
                },
                provider: {
                    enum: Object.values(InformationUpdateProvider),
                    default: InformationUpdateProvider.MALOU,
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        additionalProperties: false,
                        properties: {
                            field: {
                                type: 'string',
                            },
                            reason: {
                                enum: Object.values(InformationUpdateErrorReason),
                            },
                        },
                        required: ['reason'],
                    },
                    default: undefined,
                },
                updateDoneAt: {
                    description: 'Date when the update was done, null if not done yet',
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
            },
            required: ['key', 'status', 'provider'],
            title: 'PlatformState',
        },
        Data: {
            type: 'object',
            additionalProperties: false,
            properties: {
                isClosedTemporarily: {
                    type: 'boolean',
                    nullable: true,
                },
                regularHours: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/RegularHour',
                    },
                    default: undefined,
                },
                categoryName: {
                    type: 'string',
                    nullable: true,
                },
                secondaryCategoriesNames: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    default: undefined,
                },
                shortDescription: {
                    type: 'string',
                    nullable: true,
                },
                longDescription: {
                    type: 'string',
                    nullable: true,
                },
                website: {
                    type: 'string',
                    format: 'uri',
                    nullable: true,
                    validate: {
                        validator(v) {
                            return v === null || isValidUrl(v, { allowEmpty: true });
                        },
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                    set: cleanUrl,
                },
                coverUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                openingDate: {
                    type: 'string',
                    format: 'date-time',
                    nullable: true,
                },
                attributes: {
                    type: 'array',
                    nullable: true,
                    items: {
                        $ref: '#/definitions/Attribute',
                    },
                    default: undefined,
                },
                address: {
                    $ref: '#/definitions/Address',
                    nullable: true,
                },
                latlng: {
                    $ref: '#/definitions/Latlng',
                    nullable: true,
                },
                name: {
                    type: 'string',
                    nullable: true,
                },
                logoUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                menuUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator(v) {
                            return v === null || isValidUrl(v, { allowEmpty: true });
                        },
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                    set: cleanUrl,
                    nullable: true,
                },
                orderUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator(v) {
                            return v === null || isValidUrl(v, { allowEmpty: true });
                        },
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                    set: cleanUrl,
                    nullable: true,
                },
                reservationUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator(v) {
                            return v === null || isValidUrl(v, { allowEmpty: true });
                        },
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                    set: cleanUrl,
                    nullable: true,
                },
                socialNetworkUrls: {
                    type: 'array',
                    items: {
                        type: 'object',
                        additionalProperties: false,
                        properties: {
                            key: {
                                enum: Object.values(SocialNetworkKey),
                            },
                            url: {
                                type: 'string',
                                format: 'uri',
                            },
                        },
                        required: ['key', 'url'],
                    },
                    validate: {
                        validator: checkSocialNetworkUrls,
                        message: (props) => `Social network urls should be valid, value: ${props.value}`,
                    },
                    set(socialNetworkUrls) {
                        return formatSocialNetworkUrls(socialNetworkUrls as { key: SocialNetworkKey; url: string }[]);
                    },
                    default: undefined,
                },
                phone: {
                    $ref: '#/definitions/Phone',
                    nullable: true,
                },
                otherHours: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/OtherHour',
                    },
                    default: undefined,
                },
                // holidays
                specialHours: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/SpecialHour',
                    },
                    default: undefined,
                },
            },
            required: [],
            title: 'Data',
        },
        Address: {
            type: 'object',
            additionalProperties: false,
            properties: {
                administrativeArea: {
                    type: 'string',
                },
                country: {
                    type: 'string',
                },
                formattedAddress: {
                    type: 'string',
                },
                locality: {
                    type: 'string',
                },
                postalCode: {
                    type: 'string',
                },
                regionCode: {
                    type: 'string',
                },
                route: {
                    type: 'string',
                },
                streetNumber: {
                    type: 'string',
                },
            },
            required: ['regionCode'],
            title: 'Address',
        },
        Attribute: {
            type: 'object',
            additionalProperties: false,
            properties: {
                name: {
                    type: 'string',
                },
                value: {
                    enum: Object.values(InformationUpdateAttributeValue),
                },
            },
            required: ['name', 'value'],
            title: 'Attribute',
        },
        Latlng: {
            type: 'object',
            additionalProperties: false,
            properties: {
                lat: {
                    type: 'number',
                },
                lng: {
                    type: 'number',
                },
            },
            required: ['lat', 'lng'],
            title: 'Latlng',
        },
        Phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'integer',
                    nullable: true,
                },
                digits: {
                    type: 'integer',
                    nullable: true,
                },
            },
            required: [],
            title: 'Phone',
        },
        RegularHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                openDay: {
                    enum: Object.values(Day),
                },
                closeDay: {
                    enum: Object.values(Day),
                },
                isClosed: {
                    type: 'boolean',
                    default: true,
                },
                openTime: {
                    type: 'string',
                    match: timeRegex,
                },
                closeTime: {
                    type: 'string',
                    match: timeRegex,
                },
            },
            required: ['closeDay', 'isClosed', 'openDay'],
            title: 'RegularHour',
        },
        OtherHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                hoursTypeId: {
                    type: 'string',
                },
                periods: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/RegularHour',
                    },
                },
            },
            required: ['hoursTypeId', 'periods'],
            title: 'OtherHour',
        },
        SpecialHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                startDate: {
                    $ref: '#/definitions/Date',
                },
                openTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                endDate: {
                    $ref: '#/definitions/Date',
                },
                closeTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                isClosed: {
                    type: 'boolean',
                    default: false,
                },
            },
            required: ['isClosed', 'startDate'],
            title: 'SpecialHour',
        },
        Date: {
            type: 'object',
            additionalProperties: false,
            properties: {
                day: {
                    type: 'integer',
                },
                year: {
                    type: 'integer',
                },
                month: {
                    type: 'integer',
                },
            },
            required: ['day', 'month', 'year'],
            title: 'Date',
        },
    },
} as const satisfies JSONSchemaExtraProps;
