import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { mergedInformationUpdateJSONSchema } from './merged-information-update-schema';

const mergedInformationUpdateSchema = createMongooseSchemaFromJSONSchema(mergedInformationUpdateJSONSchema);

mergedInformationUpdateSchema.index({ restaurantId: 1 }, { unique: true });
mergedInformationUpdateSchema.index({ hasOngoingOperation: 1 });

export type IMergedInformationUpdate = FromSchema<
    typeof mergedInformationUpdateJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const MergedInformationUpdateModel = mongoose.model<IMergedInformationUpdate>(
    mergedInformationUpdateJSONSchema.title,
    mergedInformationUpdateSchema
);
