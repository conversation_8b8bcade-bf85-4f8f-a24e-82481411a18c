import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { PlatformKey } from '@malou-io/package-utils';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { informationUpdateJSONSchema } from './information-update-schema';

const informationUpdateSchema = createMongooseSchemaFromJSONSchema(informationUpdateJSONSchema);

informationUpdateSchema.index({ restaurantId: 1 });

export type IInformationUpdate = FromSchema<
    typeof informationUpdateJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IInformationUpdateData = IInformationUpdate['data'];
export type IInformationUpdatePlatformState = IInformationUpdate['platformStates'][0];
export type MergedInformationUpdateByRestaurantId = {
    restaurantId: string;
    mergedInformationUpdateByPlatform: MergedInformationUpdate[];
    platformsWithPendingOperations: PlatformKey[];
};
export type MergedInformationUpdate = {
    platformState: IInformationUpdatePlatformState;
    previousData: IInformationUpdateData;
    mergedData: IInformationUpdateData;
    lastValidatedAt?: Date;
};

export type InformationUpdateFieldsSent =
    | keyof IInformationUpdate['data']
    | 'description'
    | 'xUrl'
    | 'instagramUrl'
    | 'facebookUrl'
    | 'linkedinUrl'
    | 'youtubeUrl'
    | 'pinterestUrl'
    | 'tiktokUrl';

/**
 * This is a map of the keys that can be sent in an information update
 *
 * This object has an equivalent in the frontend : enum "InformationSent"
 * So if you change this object, you should change the enum in the frontend
 */
export const InformationUpdateFieldsSentMap: Record<InformationUpdateFieldsSent, InformationUpdateFieldsSent> = Object.freeze({
    name: 'name',
    coverUrl: 'coverUrl',
    logoUrl: 'logoUrl',
    address: 'address',
    website: 'website',
    categoryName: 'categoryName',
    secondaryCategoriesNames: 'secondaryCategoriesNames',
    menuUrl: 'menuUrl',
    phone: 'phone',
    openingDate: 'openingDate',
    regularHours: 'regularHours', // (monday, tuesday ...)
    specialHours: 'specialHours', // exceptional (holidays ...)
    otherHours: 'otherHours', // service (happy hours, delivery ...)
    shortDescription: 'shortDescription',
    longDescription: 'longDescription',
    description: 'description',
    isClosedTemporarily: 'isClosedTemporarily',
    attributes: 'attributes',
    orderUrl: 'orderUrl',
    reservationUrl: 'reservationUrl',
    xUrl: 'xUrl',
    instagramUrl: 'instagramUrl',
    facebookUrl: 'facebookUrl',
    linkedinUrl: 'linkedinUrl',
    youtubeUrl: 'youtubeUrl',
    pinterestUrl: 'pinterestUrl',
    tiktokUrl: 'tiktokUrl',
    socialNetworkUrls: 'socialNetworkUrls',

    // I think this is not really used, because we won't display at user 'yes we updated your latlng' that sounds weird..
    // its linked with address
    latlng: 'latlng',
});

export const InformationUpdateModel = mongoose.model<IInformationUpdate>(informationUpdateJSONSchema.title, informationUpdateSchema);
