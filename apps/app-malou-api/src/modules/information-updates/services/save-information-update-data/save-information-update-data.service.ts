import { singleton } from 'tsyringe';

import { IInformationUpdate, IInformationUpdateData } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { GetUpcomingPlatformsStatesService } from ':modules/information-updates/services/get-upcoming-platform-states.service';
import { MergeInformationUpdateService } from ':modules/information-updates/services/merge-information-update/merge-information-update.service';
import { ScheduleStoreLocatorDeploymentService } from ':modules/store-locator/services/schedule-store-locator-deployment/schedule-store-locator-deployment.service';

@singleton()
export class SaveInformationUpdateDataService {
    constructor(
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _mergeInformationUpdateService: MergeInformationUpdateService,
        private readonly _getUpcomingPlatformsStatesService: GetUpcomingPlatformsStatesService,
        private readonly _scheduleStoreLocatorDeploymentService: ScheduleStoreLocatorDeploymentService
    ) {}

    async execute(restaurantId: string, data: IInformationUpdateData, previousData: IInformationUpdateData): Promise<IInformationUpdate> {
        const [existingInformationUpdate, latestInformationUpdate] = await Promise.all([
            this._informationUpdatesRepository.getPendingInformationUpdateByRestaurantId(restaurantId),
            this._informationUpdatesRepository.getLatestInformationUpdateByRestaurantId(restaurantId),
        ]);

        // Schedule store locator deployment
        void this._scheduleStoreLocatorDeploymentService
            .execute({ restaurantId })
            .catch(this._scheduleStoreLocatorDeploymentService.handleSchedulingError);

        // Update data if existing information update
        if (existingInformationUpdate) {
            const newData = this._mergeInformationUpdateService.mergeInformationUpdateData(existingInformationUpdate.data ?? {}, data);
            const newPlatformStates = await this._getUpcomingPlatformsStatesService.execute({ restaurantId, data: newData });
            const platformStates = this._mergeInformationUpdateService.mergeInformationUpdatePlatformStates(
                existingInformationUpdate.platformStates,
                newPlatformStates
            );

            const infoUpdate = (await this._informationUpdatesRepository.findOneAndUpdate({
                filter: { _id: existingInformationUpdate._id },
                update: {
                    data: newData,
                    platformStates,
                },
                options: {
                    lean: true,
                },
            })) as IInformationUpdate; // Can't be null here because we retrieved existingInformationUpdate from the db before so it exists
            await this._mergeInformationUpdateService.rebuildMergeInformationUpdateData([restaurantId]);
            return infoUpdate;
        }

        // If information update is the first one ever, send all data (updated and previous data, with data values taking predominance over previousData values)
        const dataToUpdate = isNotNil(latestInformationUpdate) ? data : { ...previousData, ...data };

        // Else, create it, and add upcoming platformStates
        const platformStates = await this._getUpcomingPlatformsStatesService.execute({ restaurantId, data: dataToUpdate });

        const infoUpdate = await this._informationUpdatesRepository.createPendingInformationUpdate(restaurantId, {
            platformStates,
            data: dataToUpdate,
            previousData,
        });
        await this._mergeInformationUpdateService.rebuildMergeInformationUpdateData([restaurantId]);
        return infoUpdate;
    }
}
