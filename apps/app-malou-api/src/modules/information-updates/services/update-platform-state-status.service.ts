import { injectable } from 'tsyringe';

import {
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    InformationUpdateSupportedPlatformKey,
} from '@malou-io/package-utils';

import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { MergeInformationUpdateService } from ':modules/information-updates/services/merge-information-update/merge-information-update.service';

@injectable()
export class UpdatePlatformStateStatusService {
    constructor(
        private _informationUpdatesRepository: InformationUpdatesRepository,
        private _mergeInformationUpdateService: MergeInformationUpdateService
    ) {}

    async updateInformationUpdatePlatformStateStatus({
        restaurantId,
        platformKey,
        status,
        provider,
    }: {
        restaurantId: string;
        platformKey: InformationUpdateSupportedPlatformKey;
        status: InformationUpdatePlatformStateStatus;
        provider: InformationUpdateProvider;
    }) {
        const informationUpdates = await this._informationUpdatesRepository.getAllValidatedInformationUpdateByRestaurantId(restaurantId);

        const informationUpdatesToUpdate = informationUpdates.filter((informationUpdate) => {
            const platformStatus = informationUpdate.platformStates.find((e) => e.key === platformKey);

            return (
                !platformStatus ||
                [
                    InformationUpdatePlatformStateStatus.PENDING,
                    InformationUpdatePlatformStateStatus.ERROR,
                    InformationUpdatePlatformStateStatus.MANUAL_UPDATE_ERROR,
                    InformationUpdatePlatformStateStatus.BAD_ACCESS,
                    InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE,
                    InformationUpdatePlatformStateStatus.INVALID_PAGE,
                ].includes(platformStatus.status)
            );
        });

        const bulkOperations = informationUpdatesToUpdate.map((informationUpdate) => {
            const platformStatus = informationUpdate.platformStates.find((e) => e.key === platformKey);
            const updateDoneAt = status === InformationUpdatePlatformStateStatus.DONE ? new Date() : undefined;
            if (platformStatus) {
                platformStatus.status = status;
                platformStatus.updateDoneAt = updateDoneAt;
                platformStatus.provider = provider;
            } else {
                informationUpdate.platformStates.push({
                    key: platformKey,
                    status,
                    updateDoneAt,
                    provider,
                });
            }

            return {
                updateOne: {
                    filter: {
                        _id: informationUpdate._id,
                    },
                    update: {
                        $set: {
                            platformStates: informationUpdate.platformStates,
                        },
                    },
                },
            };
        });

        if (!bulkOperations.length) {
            return;
        }

        await this._informationUpdatesRepository.bulkOperations({ operations: bulkOperations, options: { ordered: false } });
        await this._mergeInformationUpdateService.rebuildMergeInformationUpdateData([restaurantId]);
    }
}
