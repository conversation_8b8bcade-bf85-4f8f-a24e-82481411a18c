import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { DbId, EntityRepository, IInformationUpdate, InformationUpdateModel, toDbId, toDbIds } from '@malou-io/package-models';
import { DateFilter, InformationUpdateStatus } from '@malou-io/package-utils';

@singleton()
export class InformationUpdatesRepository extends EntityRepository<IInformationUpdate> {
    constructor() {
        super(InformationUpdateModel);
    }

    async getInformationUpdatesByRestaurantIdCount(restaurantId: string, dateFilter: DateFilter): Promise<number> {
        return this.countDocuments({
            filter: {
                restaurantId: toDbId(restaurantId),
                createdAt: dateFilter,
            },
        });
    }

    async getPendingInformationUpdateByRestaurantId(restaurantId: string): Promise<IInformationUpdate | null> {
        return this.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                status: InformationUpdateStatus.PENDING,
            },
            options: { lean: true },
        });
    }

    async getAllValidatedInformationUpdateByRestaurantId(restaurantId: string): Promise<IInformationUpdate[]> {
        return this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                status: InformationUpdateStatus.VALIDATED,
            },
            options: { lean: true },
        });
    }

    async getAllInformationUpdateByRestaurantId(restaurantId: string, statuses?: InformationUpdateStatus[]): Promise<IInformationUpdate[]> {
        return this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                status: { $in: statuses ?? [InformationUpdateStatus.VALIDATED] },
            },
            options: { lean: true },
        });
    }

    async getAllInformationUpdateByForRestaurants(
        restaurantIds: string[],
        statuses?: InformationUpdateStatus[]
    ): Promise<IInformationUpdate[]> {
        return this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
                status: { $in: statuses ?? [InformationUpdateStatus.VALIDATED] },
            },
            options: { lean: true },
        });
    }

    async createPendingInformationUpdate(
        restaurantId: string,
        data: Omit<IInformationUpdate, '_id' | 'createdAt' | 'updatedAt' | 'restaurantId' | 'status'>
    ): Promise<IInformationUpdate> {
        return this.create({
            data: {
                ...data,
                restaurantId: toDbId(restaurantId),
                status: InformationUpdateStatus.PENDING,
            },
        });
    }

    async setInformationUpdateAsValidatedAndSavePlatformStates(
        _id: DbId,
        platformStates: IInformationUpdate['platformStates']
    ): Promise<void> {
        await this.findOneAndUpdate({
            filter: { _id },
            update: {
                platformStates,
                status: InformationUpdateStatus.VALIDATED,
                validatedAt: new Date(),
            },
        });
    }

    async getInformationUpdatesToPublish() {
        const informationUpdatesToPublish = await this.find({
            filter: {
                status: InformationUpdateStatus.PENDING,
                updatedAt: { $lte: DateTime.now().minus({ minutes: 5 }).toJSDate() },
            },
            options: { lean: true },
        });

        return informationUpdatesToPublish;
    }

    async getLatestInformationUpdateByRestaurantId(restaurantId: string) {
        return this.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
            },
            projection: { validatedAt: 1, createdAt: 1, status: 1 },
            options: { sort: { createdAt: -1 }, lean: true },
        });
    }
}
