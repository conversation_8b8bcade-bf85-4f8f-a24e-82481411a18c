import { singleton } from 'tsyringe';

import { GetMergedInformationUpdateBodyDto } from '@malou-io/package-dto';
import { IInformationUpdate, IInformationUpdateData, MergedInformationUpdateByRestaurantId } from '@malou-io/package-models';

import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { MergeInformationUpdateService } from ':modules/information-updates/services/merge-information-update/merge-information-update.service';
import { SaveInformationUpdateDataService } from ':modules/information-updates/services/save-information-update-data/save-information-update-data.service';

@singleton()
export class InformationUpdatesUseCases {
    constructor(
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _mergeInformationUpdateService: MergeInformationUpdateService,
        private readonly _saveInformationUpdateDataService: SaveInformationUpdateDataService
    ) {}

    async saveInformationUpdateData(
        restaurantId: string,
        data: IInformationUpdateData,
        previousData: IInformationUpdateData
    ): Promise<IInformationUpdate> {
        return this._saveInformationUpdateDataService.execute(restaurantId, data, previousData);
    }

    async getMergedInformationUpdateData(dataToFetch: GetMergedInformationUpdateBodyDto): Promise<MergedInformationUpdateByRestaurantId[]> {
        return this._mergeInformationUpdateService.getMergedInformationUpdateData(dataToFetch);
    }

    async hasRestaurantEverBeenUpdated(restaurantId: string): Promise<boolean> {
        const informationUpdates = await this._informationUpdatesRepository.getAllValidatedInformationUpdateByRestaurantId(restaurantId);

        return informationUpdates.length !== 0;
    }
}
