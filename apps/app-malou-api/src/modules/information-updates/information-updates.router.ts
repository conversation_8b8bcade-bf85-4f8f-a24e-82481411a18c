import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { casl } from ':helpers/casl/middlewares';
import { RequestWithPermissions } from ':helpers/utils.types';
import InformationUpdatesController from ':modules/information-updates/information-updates.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class InformationUpdatesRouter extends AbstractRouter {
    constructor(private _informationUpdatesController: InformationUpdatesController) {
        super();
    }

    init(): Router {
        this.router.post(
            '/save-information-update-data',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) =>
                this._informationUpdatesController.handleSaveInformationUpdateData(req, res, next)
        );

        this.router.post(
            '/update-information-update-platform-state-status',
            authorize(),
            (req: Request, res: Response, next: NextFunction) =>
                this._informationUpdatesController.handleUpdateInformationUpdatePlatformStateStatus(req, res, next)
        );

        this.router.post('/get-merged-information-update-data', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._informationUpdatesController.handleGetMergedInformationUpdateData(req, res, next)
        );

        this.router.post('/has-restaurant-ever-been-updated', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._informationUpdatesController.handleHasRestaurantEverBeenUpdated(req, res, next)
        );

        this.router.get(
            '/restaurants/:restaurant_id/get-detailed-update-statuses',
            authorize(),
            (req: any, res: Response, next: NextFunction) =>
                this._informationUpdatesController.handleGetDetailedUpdateStatuses(req, res, next)
        );

        return this.router;
    }
}
