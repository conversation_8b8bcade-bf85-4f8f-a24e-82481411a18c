/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformAccessType, PlatformKey } from '@malou-io/package-utils';

import { MergedInformationUpdatesRepository } from ':modules/information-updates/merged-information-updates.repository';
import { MergeInformationUpdateService } from ':modules/information-updates/services/merge-information-update/merge-information-update.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(
        private readonly _mergeInformationUpdateService: MergeInformationUpdateService,
        private readonly _mergedInformationUpdatesRepository: MergedInformationUpdatesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute() {
        const allRestaurantsInTheDb = await this._restaurantsRepository.find({
            filter: {},
            projection: { _id: 1, access: 1 },
            options: { lean: true },
        });
        const mergeInfoDataParams = allRestaurantsInTheDb.map((restaurant) => ({
            restaurantId: restaurant._id.toString(),
            platformKeys: restaurant.access
                .filter(
                    (access) =>
                        access.active &&
                        access.accessType !== PlatformAccessType.AUTO &&
                        access.platformKey !== ('pagesjaunes' as PlatformKey)
                )
                .map((access) => access.platformKey),
        }));
        const mergedInformationUpdateByPlatform =
            await this._mergeInformationUpdateService.computeMergeInformationUpdateData(mergeInfoDataParams);

        let index = 0;
        for (const mergedInformationUpdate of mergedInformationUpdateByPlatform) {
            index++;
            await this._mergedInformationUpdatesRepository.upsert({
                filter: { restaurantId: toDbId(mergedInformationUpdate.restaurantId) },
                update: {
                    ...mergedInformationUpdate,
                    restaurantId: toDbId(mergedInformationUpdate.restaurantId),
                    hasOngoingOperation: mergedInformationUpdate.mergedInformationUpdateByPlatform?.length > 0,
                } as unknown as any,
            });
            console.log(
                `[${index}] ${mergedInformationUpdate.restaurantId} - ${mergedInformationUpdate.mergedInformationUpdateByPlatform.length}`
            );
        }
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
